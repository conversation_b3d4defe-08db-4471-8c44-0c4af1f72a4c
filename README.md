# Hello World C++ 项目

这是一个简单的 C++ Hello World 控制台应用程序，使用 CMake 构建系统。

## 项目结构

```
myproject/
├── CMakeLists.txt          # CMake 配置文件
├── src/                    # 源代码目录
│   └── main.cpp           # 主程序文件
└── README.md              # 项目说明文件（本文件）
```

## 系统要求

- Windows 操作系统
- Visual Studio 2017 或更高版本（MSVC 编译器）
- CMake 3.10 或更高版本

## 构建说明

### 使用 CMake 构建

1. 在项目根目录创建构建目录：
   ```cmd
   mkdir build
   cd build
   ```

2. 生成 Visual Studio 项目文件：
   ```cmd
   cmake ..
   ```

3. 编译项目：
   ```cmd
   cmake --build . --config Release
   ```

4. 运行程序：
   ```cmd
   bin\Release\HelloWorld.exe
   ```

### 使用 Visual Studio

1. 在项目根目录创建构建目录并生成项目文件：
   ```cmd
   mkdir build
   cd build
   cmake ..
   ```

2. 打开生成的 `HelloWorld.sln` 文件

3. 在 Visual Studio 中构建并运行项目

## 预期输出

程序运行后将在控制台输出：
```
Hello, World!
欢迎使用 C++ 项目！
```

## 扩展说明

这个项目结构为将来的扩展做好了准备，您可以：
- 在 `src/` 目录中添加更多源文件
- 修改 `CMakeLists.txt` 来添加库依赖
- 扩展为更复杂的应用程序（如游戏项目）
